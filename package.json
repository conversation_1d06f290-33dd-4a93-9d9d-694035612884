{"name": "qr-code-generator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"@radix-ui/react-select": "^2.2.6", "@tailwindcss/vite": "^4.1.14", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.14"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.6.2", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.4", "babel-plugin-react-compiler": "^19.1.0-rc.3", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tw-animate-css": "^1.4.0", "typescript": "~5.9.3", "typescript-eslint": "^8.45.0", "vite": "^7.1.7"}}